package com.example.gemini;

import java.io.BufferedReader;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

public class ServerInfoReader {
    
    private static final Map<String, String> serverInfo = new HashMap<>();
    private static boolean directoryInitialized = false;
    
    /**
     * Initialize Gemini directory (create if needed)
     */
    public static void initializeDirectory() {
        if (directoryInitialized) {
            return;
        }

        try {
            // Get Minecraft directory and create Gemini folder path
            Path minecraftDir = getMinecraftDirectory();
            Path geminiDir = minecraftDir.resolve("Gemini");

            // Create Gemini directory if it doesn't exist
            if (!Files.exists(geminiDir)) {
                Files.createDirectories(geminiDir);
                System.out.println("[Server Info] Created Gemini directory: " + geminiDir.toString());
                System.out.println("[Server Info] Please add your server data files (.txt or .json) to this directory");
            }

            directoryInitialized = true;

        } catch (Exception e) {
            System.err.println("[Server Info] Error initializing directory: " + e.getMessage());
            e.printStackTrace();
            directoryInitialized = true;
        }
    }
    
    /**
     * Get Minecraft directory
     */
    private static Path getMinecraftDirectory() {
        // Get current working directory (should be Minecraft instance directory)
        String currentDir = System.getProperty("user.dir");
        return Paths.get(currentDir);
    }

    /**
     * Load all .txt and .json files from Gemini directory
     */
    private static void loadAllTextFiles(Path geminiDir) throws IOException {
        if (!Files.exists(geminiDir) || !Files.isDirectory(geminiDir)) {
            System.err.println("[Server Info] Gemini directory does not exist: " + geminiDir);
            return;
        }

        // Find and load ALL .txt and .json files in the directory
        try {
            Files.list(geminiDir)
                .filter(path -> {
                    String fileName = path.getFileName().toString().toLowerCase();
                    boolean isTxtFile = fileName.endsWith(".txt");
                    boolean isJsonFile = fileName.endsWith(".json");
                    boolean isFile = Files.isRegularFile(path);
                    boolean isReadable = Files.isReadable(path);

                    return (isTxtFile || isJsonFile) && isFile && isReadable;
                })
                .forEach(path -> {
                    try {
                        loadFile(path);
                    } catch (Exception e) {
                        System.err.println("[Server Info] ✗ Failed to load " + path.getFileName() + ": " + e.getMessage());
                    }
                });
        } catch (Exception e) {
            System.err.println("[Server Info] Error listing directory: " + e.getMessage());
            e.printStackTrace();
        }


    }

    /**
     * Load a specific text or JSON file with comprehensive error handling
     */
    private static void loadFile(Path filePath) throws IOException {

        // Verify file exists and is readable
        if (!Files.exists(filePath)) {
            throw new IOException("File does not exist: " + filePath);
        }
        if (!Files.isReadable(filePath)) {
            throw new IOException("File is not readable: " + filePath);
        }

        try (BufferedReader reader = Files.newBufferedReader(filePath, StandardCharsets.UTF_8)) {
            StringBuilder content = new StringBuilder();
            String line;
            int lineCount = 0;

            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
                lineCount++;
            }

            // Store content with file name as key (without extension)
            String fileName = filePath.getFileName().toString();
            String key = fileName.replace(".txt", "").replace(".json", "");
            String contentStr = content.toString().trim();

            // Special processing for JSON files
            if (fileName.toLowerCase().endsWith(".json")) {
                contentStr = processJsonFile(contentStr, fileName);
            }

            // Ensure content is not empty
            if (contentStr.isEmpty()) {
                contentStr = "Файл порожній: " + fileName;
            }

            serverInfo.put(key, contentStr);

            // Log detailed file loading info
            System.out.println("[Server Info] ✓ Loaded " + fileName + ": " + lineCount + " lines, " + contentStr.length() + " characters");



        } catch (Exception e) {
            System.err.println("[Server Info] ✗ Exception loading file " + filePath.getFileName() + ": " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }


    
    /**
     * Get all server information as a single string (reads files fresh each time)
     */
    public static String getAllServerInfo() {
        // Initialize directory if needed
        initializeDirectory();

        // Clear old data and reload fresh
        serverInfo.clear();

        try {
            Path minecraftDir = getMinecraftDirectory();
            Path geminiDir = minecraftDir.resolve("Gemini");



            // Load all .txt and .json files fresh
            loadAllTextFiles(geminiDir);

        } catch (Exception e) {
            System.err.println("[Server Info] Error reading fresh data: " + e.getMessage());
            serverInfo.put("Помилка", "Не вдалося завантажити інформацію про сервер з папки Gemini");
        }

        StringBuilder allInfo = new StringBuilder();
        final int MAX_CONTEXT_LENGTH = 100000; // 100KB limit for context
        int currentLength = 0;

        // Sort files by importance (smaller files first, then larger ones)
        java.util.List<Map.Entry<String, String>> sortedEntries = new java.util.ArrayList<>(serverInfo.entrySet());
        sortedEntries.sort((a, b) -> Integer.compare(a.getValue().length(), b.getValue().length()));

        for (Map.Entry<String, String> entry : sortedEntries) {
            String fileName = entry.getKey();
            String content = entry.getValue();

            // Check if adding this file would exceed the limit
            int entryLength = fileName.length() + content.length() + 20; // +20 for formatting

            if (currentLength + entryLength > MAX_CONTEXT_LENGTH) {
                // Truncate the content to fit
                int availableSpace = MAX_CONTEXT_LENGTH - currentLength - fileName.length() - 20;
                if (availableSpace > 1000) { // Only include if we have at least 1KB space
                    content = content.substring(0, Math.min(content.length(), availableSpace)) + "\n[ФАЙЛ ОБРІЗАНО ЧЕРЕЗ ОБМЕЖЕННЯ РОЗМІРУ]";
                    allInfo.append("=== ").append(fileName).append(" ===\n");
                    allInfo.append(content).append("\n\n");
                    currentLength += fileName.length() + content.length() + 20;
                }
                break; // Stop adding more files
            } else {
                allInfo.append("=== ").append(fileName).append(" ===\n");
                allInfo.append(content).append("\n\n");
                currentLength += entryLength;
            }
        }

        String result = allInfo.toString().trim();

        // Log final statistics
        System.out.println("[Server Info] Total files loaded: " + serverInfo.size());
        System.out.println("[Server Info] Files included in context: " + sortedEntries.size());
        System.out.println("[Server Info] Total context length: " + result.length() + " characters (limit: " + MAX_CONTEXT_LENGTH + ")");



        return result;
    }
    
    /**
     * Check if a question is related to the server
     */
    public static boolean isServerRelated(String question) {
        String lowerQuestion = question.toLowerCase();
        
        // Server-related keywords
        String[] serverKeywords = {
            "сервер", "картопляний", "майнкрафт", "minecraft", 
            "правила", "держави", "рп", "політика", "команди",
            "/sit", "/crawl", "платівки", "виживання", "ванільний",
            "потрапити", "події", "соціальні мережі", "вікі"
        };
        
        for (String keyword : serverKeywords) {
            if (lowerQuestion.contains(keyword)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Get server info for context (always fresh)
     */
    public static String getServerContext() {
        return getAllServerInfo();
    }

    /**
     * Get bot name from Name.txt file
     */
    public static String getBotName() {
        // Initialize directory if needed
        initializeDirectory();

        try {
            Path minecraftDir = getMinecraftDirectory();
            Path geminiDir = minecraftDir.resolve("Gemini");
            Path nameFile = geminiDir.resolve("Name.txt");

            // Check if Name.txt exists
            if (!Files.exists(nameFile)) {
                return "Кікерша";
            }

            // Read bot name from file
            try (BufferedReader reader = Files.newBufferedReader(nameFile, StandardCharsets.UTF_8)) {
                String botName = reader.readLine();
                if (botName != null && !botName.trim().isEmpty()) {
                    botName = botName.trim();
                    return botName;
                } else {
                    return "Кікерша";
                }
            }

        } catch (Exception e) {
            System.err.println("[Server Info] Error reading bot name: " + e.getMessage());
            return "Кікерша"; // Fallback name
        }
    }

    /**
     * Process JSON file content to make it more readable for Gemini
     */
    private static String processJsonFile(String jsonContent, String fileName) {
        try {
            // Parse JSON
            JsonElement jsonElement = JsonParser.parseString(jsonContent);

            // Convert to pretty-printed format for better readability
            Gson gson = new GsonBuilder().setPrettyPrinting().create();
            String prettyJson = gson.toJson(jsonElement);

            // Add header to indicate this is JSON data
            StringBuilder result = new StringBuilder();
            result.append("=== JSON ДАНІ З ФАЙЛУ ").append(fileName).append(" ===\n\n");

            // If it's a JSON object, try to extract key information
            if (jsonElement.isJsonObject()) {
                JsonObject jsonObject = jsonElement.getAsJsonObject();

                // Add summary of main keys
                result.append("ОСНОВНІ РОЗДІЛИ:\n");
                for (String key : jsonObject.keySet()) {
                    JsonElement value = jsonObject.get(key);
                    if (value.isJsonArray()) {
                        result.append("- ").append(key).append(": масив з ").append(value.getAsJsonArray().size()).append(" елементів\n");
                    } else if (value.isJsonObject()) {
                        result.append("- ").append(key).append(": об'єкт з ").append(value.getAsJsonObject().size()).append(" полями\n");
                    } else {
                        result.append("- ").append(key).append(": ").append(value.toString()).append("\n");
                    }
                }
                result.append("\n");
            }

            result.append("ПОВНИЙ JSON:\n");
            result.append(prettyJson);

            System.out.println("[Server Info] Processed JSON file " + fileName + " with " +
                             (jsonElement.isJsonObject() ? jsonElement.getAsJsonObject().size() + " main keys" : "array/primitive"));

            return result.toString();

        } catch (Exception e) {
            System.err.println("[Server Info] Error processing JSON file " + fileName + ": " + e.getMessage());
            // Return original content if JSON parsing fails
            return "=== ФАЙЛ " + fileName + " (помилка парсингу JSON) ===\n\n" + jsonContent;
        }
    }
}
