package com.example.gemini;

import net.fabricmc.api.ClientModInitializer;
import net.fabricmc.fabric.api.client.command.v2.ClientCommandManager;
import net.fabricmc.fabric.api.client.command.v2.ClientCommandRegistrationCallback;
import net.fabricmc.fabric.api.client.message.v1.ClientReceiveMessageEvents;
import net.minecraft.client.MinecraftClient;
import net.minecraft.text.Text;

import com.mojang.brigadier.arguments.IntegerArgumentType;
import com.mojang.brigadier.context.CommandContext;
import net.fabricmc.fabric.api.client.command.v2.FabricClientCommandSource;



public class Gemini implements ClientModInitializer {
	public static final String MOD_ID = "gemini";



	// Time limiting for API calls
	private static int timeLimit = 0; // Time limit in seconds (0 = no limit)
	private static long lastApiCall = 0; // Timestamp of last API call

	@Override
	public void onInitializeClient() {
		System.out.println("[Gemini Mod] 🚀 Initializing client-side mod...");

		// Register event to listen for incoming chat messages
		ClientReceiveMessageEvents.GAME.register((message, overlay) -> {
			String text = message.getString();
			handleChatMessage(text);
		});

		// Also register for CHAT events (alternative method)
		ClientReceiveMessageEvents.CHAT.register((message, signedMessage, sender, params, receptionTimestamp) -> {
			String text = message.getString();
			handleChatMessage(text);
		});

		// Register client-side /limit command
		ClientCommandRegistrationCallback.EVENT.register((dispatcher, registryAccess) -> {
			dispatcher.register(ClientCommandManager.literal("limit")
				.executes(this::showCurrentLimit)
				.then(ClientCommandManager.argument("seconds", IntegerArgumentType.integer(0))
					.executes(this::setTimeLimit)
				)
			);
		});

		System.out.println("[Gemini Mod] Initialized. Use !a or !а in chat to interact with Gemini.");
	}



	private void handleChatMessage(String text) {
		// Extract the actual message content from chat format
		// Supports both "<player> message" and "player: message" formats
		String actualMessage = text;
		String senderName = null;

		System.out.println("[Chat Parser] Raw message: " + text);

		// Try format: "<player> message" (public chat)
		if (text.contains("> ")) {
			int startBracket = text.indexOf("<");
			int endBracket = text.indexOf(">");
			int index = text.indexOf("> ");

			System.out.println("[Chat Parser] Found '> ' pattern. Start: " + startBracket + ", End: " + endBracket + ", Index: " + index);

			// Ensure brackets are in correct order and exist
			if (startBracket != -1 && endBracket != -1 && startBracket < endBracket && index != -1) {
				senderName = text.substring(startBracket + 1, endBracket);
				actualMessage = text.substring(index + 2);
				System.out.println("[Chat Parser] Parsed <player> format. Sender: " + senderName + ", Message: " + actualMessage);
			} else {
				System.out.println("[Chat Parser] Invalid bracket positions, skipping <player> format");
			}
		}
		// Try format: "player: message" (public chat)
		else if (text.contains(": ")) {
			int index = text.indexOf(": ");
			System.out.println("[Chat Parser] Found ': ' pattern at index: " + index);

			if (index != -1 && index > 0) {
				senderName = text.substring(0, index);
				actualMessage = text.substring(index + 2);
				System.out.println("[Chat Parser] Parsed player: format. Sender: " + senderName + ", Message: " + actualMessage);
			} else {
				System.out.println("[Chat Parser] Invalid ': ' position, skipping player: format");
			}
		}


		// Skip if this is our own bot message
		if (senderName != null && (senderName.equals("Amazo") || senderName.equals("Кікерша"))) {
			System.out.println("[Chat Parser] Skipping own bot message from: " + senderName);
			return;
		}

		// Check for different command types
		final String prompt;

		if (actualMessage.startsWith("!a ")) {
			prompt = actualMessage.substring(3).trim();
		} else if (actualMessage.startsWith("!а ")) {
			prompt = actualMessage.substring(3).trim();
		} else {
			prompt = null;
		}

		// Handle text queries
		if (prompt != null) {
			if (prompt.isEmpty()) {
				return;
			}

			// Check time limit before processing
			if (!canMakeApiCall()) {
				return; // Silently ignore if time limit not reached
			}

			// Update last API call timestamp
			lastApiCall = System.currentTimeMillis();

			// Process asynchronously to avoid blocking the client
			new Thread(() -> {
				try {
					// Try the new Gemini SDK client
					String apiResponse = null;
					try {
						apiResponse = NewGeminiClient.getResponse(prompt);
					} catch (Exception apiException) {
						System.err.println("[Gemini Mod] API failed: " + apiException.getMessage());
						apiResponse = "Помилка API. Спробуйте пізніше.";
					}

					// Clean response: replace newlines with spaces and strictly limit length for Minecraft chat
					String limitedResponse = apiResponse;
					if (limitedResponse != null) {
						// Replace newlines with spaces for better chat display
						limitedResponse = limitedResponse.replace("\n", " ").replace("\r", " ");

						// Check if response exceeds 256 characters
						if (limitedResponse.length() > 256) {
							// Only truncate as last resort
							limitedResponse = limitedResponse.substring(0, 256);
						}
					}

					// Clean response and prepare for direct output (no bot name prefix)
					String finalResponse = limitedResponse;

					if (finalResponse != null) {
						// Remove any existing "Gemini:" prefix
						if (finalResponse.toLowerCase().startsWith("gemini:")) {
							finalResponse = finalResponse.substring(7).trim(); // Remove "Gemini:" and trim
						}

						// Remove any other common prefixes
						String[] prefixes = {"ai:", "бот:", "помічник:", "асистент:"};
						for (String prefix : prefixes) {
							if (finalResponse.toLowerCase().startsWith(prefix)) {
								finalResponse = finalResponse.substring(prefix.length()).trim();
								break;
							}
						}
					}

					// Final check for 256 character limit
					if (finalResponse != null && finalResponse.length() > 256) {
						// Emergency truncation without "..." to maintain completeness
						finalResponse = finalResponse.substring(0, 256);
					}

					final String response = finalResponse;

					// Execute on client thread to send chat message
					MinecraftClient.getInstance().execute(() -> {
						try {
							if (response != null && !response.isEmpty()) {
								// Send response as chat message from your account
								MinecraftClient.getInstance().player.networkHandler.sendChatMessage(response);
							} else {
								MinecraftClient.getInstance().player.networkHandler.sendChatMessage("No response received from Gemini");
							}
						} catch (Exception e) {
							System.err.println("[Gemini Mod] Error sending chat message: " + e.getMessage());
						}
					});
				} catch (Exception e) {
					System.err.println("[Gemini Mod] Error processing !ask command: " + e.getMessage());
					e.printStackTrace();

					MinecraftClient.getInstance().execute(() -> {
						try {
							MinecraftClient.getInstance().player.networkHandler.sendChatMessage("Error: " + e.getMessage());
						} catch (Exception ex) {
							System.err.println("[Gemini Mod] Error sending error message: " + ex.getMessage());
						}
					});
				}
			}).start();
		}


}



// Client command methods
	private int showCurrentLimit(CommandContext<FabricClientCommandSource> context) {
		String message;
		if (timeLimit == 0) {
			message = "Поточне обмеження: немає (API доступний завжди)";
		} else {
			message = "Поточне обмеження: " + timeLimit + " секунд між запитами";
		}
		context.getSource().sendFeedback(Text.literal(message));
		return 1;
	}

	private int setTimeLimit(CommandContext<FabricClientCommandSource> context) {
		int newLimit = IntegerArgumentType.getInteger(context, "seconds");

		timeLimit = newLimit;
		lastApiCall = 0; // Reset timer

		String message;
		if (timeLimit == 0) {
			message = "Обмеження часу вимкнено";
		} else {
			message = "Час збережено: " + timeLimit + " секунд між запитами";
		}

		context.getSource().sendFeedback(Text.literal(message));
		return 1;
	}

	// Method to check if API call is allowed based on time limit
	private boolean canMakeApiCall() {
		if (timeLimit == 0) {
			return true; // No time limit
		}

		long currentTime = System.currentTimeMillis();
		long timeSinceLastCall = (currentTime - lastApiCall) / 1000; // Convert to seconds

		return timeSinceLastCall >= timeLimit;
	}

	// Helper method to send chat message
	private void sendChatMessage(String message) {
		MinecraftClient.getInstance().execute(() -> {
			try {
				if (MinecraftClient.getInstance().player != null) {
					MinecraftClient.getInstance().player.networkHandler.sendChatMessage(message);
				}
			} catch (Exception e) {
				System.err.println("[Gemini Mod] Error sending chat message: " + e.getMessage());
			}
		});
	}
}