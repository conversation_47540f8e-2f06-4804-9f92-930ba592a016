# Підтримка JSON файлів

## Що додано

✅ **Мод тепер може читати JSON файли!**

### 🔧 Технічні зміни:

#### 1. Розширено фільтр файлів
```java
// БУЛО:
boolean isTxtFile = fileName.endsWith(".txt");
return isTxtFile && isFile && isReadable;

// СТАЛО:
boolean isTxtFile = fileName.endsWith(".txt");
boolean isJsonFile = fileName.endsWith(".json");
return (isTxtFile || isJsonFile) && isFile && isReadable;
```

#### 2. Додано обробку JSON
- Автоматичне форматування JSON для кращої читабельності
- Створення резюме основних розділів
- Обробка помилок парсингу

#### 3. Покращено логування
```
[Server Info] ✓ Loaded server_data.json: 45 lines, 1234 characters
[Server Info] Processed JSON file server_data.json with 7 main keys
```

## Як використовувати

### 📁 Структура файлів:
```
Gemini/
├── правила.txt          ✅ Текстовий файл
├── server_info.json     ✅ JSON файл
├── команди.json         ✅ JSON файл
└── історія.txt          ✅ Текстовий файл
```

### 📝 Приклад JSON файлу:
```json
{
  "server_info": {
    "name": "Картопляний",
    "version": "1.21.5",
    "type": "Survival"
  },
  "rules": [
    "Заборонено гріфінг",
    "Поважайте інших гравців"
  ],
  "commands": {
    "/sit": "Сісти на місці",
    "/crawl": "Повзати"
  },
  "locations": {
    "spawn": {
      "x": 0, "y": 64, "z": 0,
      "description": "Головний спавн"
    }
  }
}
```

## Переваги JSON

### ✅ Для організації даних:
1. **Структурованість** - чіткі розділи та ієрархія
2. **Валідація** - автоматична перевірка синтаксису
3. **Читабельність** - автоматичне форматування
4. **Гнучкість** - масиви, об'єкти, різні типи даних

### ✅ Для Gemini AI:
1. **Кращий контекст** - структуровані дані легше аналізувати
2. **Резюме розділів** - автоматично створюється опис структури
3. **Форматування** - JSON автоматично форматується для читабельності

## Обробка JSON

### 🔄 Що відбувається з JSON файлом:
1. **Парсинг** - перевірка валідності JSON
2. **Форматування** - красиве відображення з відступами
3. **Резюме** - створення опису основних розділів
4. **Заголовок** - додавання інформації про файл

### 📊 Приклад обробленого JSON:
```
=== JSON ДАНІ З ФАЙЛУ server_info.json ===

ОСНОВНІ РОЗДІЛИ:
- server_info: об'єкт з 3 полями
- rules: масив з 2 елементів
- commands: об'єкт з 2 полями
- locations: об'єкт з 1 полями

ПОВНИЙ JSON:
{
  "server_info": {
    "name": "Картопляний",
    "version": "1.21.5",
    "type": "Survival"
  },
  ...
}
```

## Сумісність

### ✅ Повна зворотна сумісність:
- Всі існуючі .txt файли працюють як раніше
- Можна змішувати .txt та .json файли
- Немає змін в API або командах

### 🔧 Обробка помилок:
- Якщо JSON невалідний - файл читається як звичайний текст
- Логування помилок парсингу
- Продовження роботи з іншими файлами

## Рекомендації

### 📋 Коли використовувати JSON:
- **Структуровані дані** (правила, команди, локації)
- **Списки та каталоги** (гравці, події, предмети)
- **Конфігурації** (налаштування, параметри)

### 📋 Коли використовувати TXT:
- **Довгі тексти** (історії, описи)
- **Неструктурована інформація** (новини, оголошення)
- **Простий контент** (короткі повідомлення)

## Тестування

Створено приклад файлу `example_server_data.json` для тестування функціональності.

**Результат:** Мод тепер підтримує як текстові, так і JSON файли для максимальної гнучкості організації серверної інформації!
