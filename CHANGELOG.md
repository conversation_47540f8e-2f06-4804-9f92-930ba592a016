# Changelog

## Версія 1.0.0 - Спрощення моду

### ❌ Видалено:
- **Генерація зображень** - команди `!g` та `!г`
- **Telegram інтеграція** - зв'язування акаунтів, link codes
- **JSON файли** - збереження даних про зв'язані акаунти
- **Приватні повідомлення** - обробка whisper команд
- **Telegram Bot Server** - окремий сервер для обробки Telegram команд

### ✅ Залишилося:
- **Основний Gemini чат** - команди `!a` та `!а`
- **Серверна інформація** - читання файлів з папки `Gemini/`
- **Карта сервера** - інтеграція з API карти
- **Обмеження швидкості** - команда `/limit`
- **Всі налаштування Gemini API** - модель, токени, параметри

### 📁 Видалені файли:
- `src/main/java/com/example/gemini/ImageGenerationHandler.java`
- `src/main/java/com/example/gemini/LinkVerificationHandler.java`
- `src/main/java/com/example/gemini/TelegramBot.java`
- `src/main/java/com/example/gemini/JsonDataManager.java`
- `TelegramBotServer.java`
- `README_TELEGRAM_LINK.md`
- `TELEGRAM_SETUP.md`
- `start_telegram_bot.bat`
- `start_telegram_bot.sh`
- `Telegram/` папка та всі JSON файли

### 🔧 Змінений код:
- **Gemini.java** - видалено всю логіку обробки зображень та Telegram
- Спрощено парсинг чату - тільки публічні повідомлення
- Видалено перевірку приватних повідомлень
- Очищено від залежностей на видалені класи

### 🎯 Результат:
Тепер мод є простим Gemini чат-ботом без зайвої складності:
- Тільки текстові запити до Gemini AI
- Читання серверної інформації з файлів
- Інтеграція з картою сервера
- Налаштування обмежень швидкості

### 📦 Компіляція:
- Мод успішно компілюється без помилок
- Розмір значно зменшився через видалення зайвого коду
- Готовий JAR файл: `build/libs/gemini-1.0.0.jar`
