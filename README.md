# Gemini Minecraft Mod

Простий Minecraft мод для взаємодії з Gemini AI через чат.

## Функціональність

### Основні команди:
- `!a <запитання>` - Задати запитання Gemini AI (англійська 'a')
- `!а <запитання>` - Задати запитання Gemini AI (українська 'а')

### Команди налаштування:
- `/limit` - Показати поточне обмеження часу між запитами
- `/limit <секунди>` - Встановити обмеження часу між запитами (0 = без обмежень)

## Особливості

### Gemini AI інтеграція:
- Використовує Google Gemini API
- Модель: `gemini-2.5-flash-lite-preview-06-17`
- Максимум 128 токенів на відповідь
- Відповіді обмежені 256 символами для Minecraft чату

### Серверна інформація:
- Читає інформацію про сервер з файлів в папці `Gemini/`
- Підтримує формати: `.txt` (текстові файли) та `.json` (структуровані дані)
- **Повний аналіз файлів:** аналізує КОЖЕН рядок файлу (навіть 100k+ рядків) з ранжуванням за релевантністю
- Інтегрується з картою сервера через API
- Відповідає на запитання про сервер Картопляний

### Обмеження швидкості:
- Можна встановити мінімальний інтервал між запитами
- За замовчуванням обмежень немає
- Запити під час кулдауну ігноруються без повідомлень

## Налаштування

### API ключ Gemini:
Змініть API ключ в файлі `src/main/java/com/example/gemini/NewGeminiClient.java`:
```java
private static final String API_KEY = "YOUR_API_KEY_HERE";
```

### Файли даних:
Розмістіть файли з інформацією про сервер в папці `Gemini/` в директорії версії Minecraft.

**Підтримувані формати:**
- `.txt` - Текстові файли (звичайний текст)
- `.json` - JSON файли (структуровані дані з автоматичним форматуванням)

**Приклад JSON файлу:**
```json
{
  "server_info": {
    "name": "Картопляний",
    "version": "1.21.5"
  },
  "rules": [
    "Заборонено гріфінг",
    "Поважайте інших гравців"
  ],
  "commands": {
    "/sit": "Сісти на місці",
    "/crawl": "Повзати"
  }
}
```

### Повний аналіз файлів:
Мод аналізує КОЖЕН рядок файлу незалежно від розміру:
- **Повне сканування:** аналізується 100% файлу (навіть 100k+ рядків)
- **Ранжування за релевантністю:** знайдені збіги сортуються за якістю
- **Топ-50 результатів:** найкращі збіги з контекстом передаються Gemini
- **Гарантія повноти:** жоден релевантний рядок не буде пропущений

Файл з 100,414 рядків буде проаналізований повністю!

## Встановлення

1. Скомпілюйте мод:
   ```
   ./gradlew build
   ```

2. Скопіюйте файл з `build/libs/` в папку `mods` Minecraft

3. Запустіть Minecraft з Fabric Loader

## Використання

1. Зайдіть на сервер
2. Напишіть в чат `!a Привіт!` або `!а Як справи?`
3. Отримайте відповідь від Gemini AI
4. Використовуйте `/limit 10` для встановлення 10-секундного кулдауну

## Технічні деталі

### Підтримувані формати чату:
- `<player> message` - стандартний формат
- `player: message` - альтернативний формат

### Файли моду:
- `Gemini.java` - Основний клас моду
- `NewGeminiClient.java` - Клієнт Gemini API
- `ServerInfoReader.java` - Читання серверної інформації
- `ServerMapReader.java` - Інтеграція з картою сервера

### Залежності:
- Fabric API
- Gson (для JSON обробки)
- Java HTTP Client (для API запитів)

## Примітки

- Мод працює тільки на клієнті (client-side)
- Потребує підключення до інтернету для роботи з Gemini API
- Відповіді автоматично обрізаються до 256 символів для сумісності з Minecraft чатом
