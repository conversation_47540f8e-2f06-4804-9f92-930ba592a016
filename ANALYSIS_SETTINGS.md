# Поточні налаштування аналізу файлів

## Стратегії за розміром файлу

### 📁 **Малі файли (<10,000 рядків)**
**Метод:** `originalSearch()`
- **Заголовок:** 50 рядків
- **Аналіз:** Весь файл повністю
- **Контекст:** 5 рядків до + 6 рядків після релевантного = 11 рядків
- **Fallback:** +2000 рядків якщо результат < 1000 символів

### 📁 **Середні файли (10,000-50,000 рядків)**
**Метод:** `enhancedSearch()`
- **Заголовок:** 50 рядків
- **Аналіз:** Весь файл, але обмежені результати
- **Максимум секцій:** 20 релевантних секцій
- **Контекст:** 10 рядків до + 11 рядків після релевантного = 21 рядок

### 📁 **Великі файли (>50,000 рядків)**
**Метод:** `smartChunkSearch()`
- **Заголовок:** 100 рядків (завжди включається)
- **Розмір фрагменту:** 1000 рядків за раз
- **Максимум фрагментів:** 10 релевантних фрагментів
- **Контекст:** 3 рядки до + 4 рядки після релевантного = 7 рядків
- **Fallback:** 2000 рядків зразка якщо нічого не знайдено

## Конкретно для файлу з 100,414 рядків

### 🔢 **Цифри аналізу:**
```
Тип: Великий файл (>50,000 рядків)
Стратегія: smartChunkSearch()

Структура аналізу:
├── Заголовок: рядки 1-100 (завжди)
├── Фрагмент 1: рядки 101-1100 (якщо релевантний)
├── Фрагмент 2: рядки 1101-2100 (якщо релевантний)
├── ...
└── Фрагмент 100: рядки 99101-100100 (якщо релевантний)

Максимум аналізованих рядків: 100 + (10 × 1000) = 10,100 рядків
Відсоток від файлу: 10,100 / 100,414 = 10.05%
```

### 📊 **Ефективність:**
- **Повний файл:** 100,414 рядків (100%)
- **Реально аналізується:** до 10,100 рядків (10.05%)
- **Завжди включається:** 100 рядків заголовку (0.1%)
- **Релевантні фрагменти:** до 10,000 рядків (9.95%)

### 🎯 **Що отримує Gemini API:**
```
=== ЗАГОЛОВОК ФАЙЛУ ===
[100 рядків з початку файлу]
=== КІНЕЦЬ ЗАГОЛОВКУ ===

=== РЕЛЕВАНТНИЙ ФРАГМЕНТ (рядки 15234-16234) ===
[~7 рядків контексту навколо знайденої інформації]
=== КІНЕЦЬ ФРАГМЕНТУ ===

=== РЕЛЕВАНТНИЙ ФРАГМЕНТ (рядки 45678-46678) ===
[~7 рядків контексту навколо знайденої інформації]
=== КІНЕЦЬ ФРАГМЕНТУ ===

[до 8 додаткових фрагментів]

Типовий розмір результату: 5,000-15,000 символів
```

## Налаштування

### ⚙️ **Параметри, які можна змінити:**

#### Для великих файлів (`smartChunkSearch`):
```java
final int CHUNK_SIZE = 1000;    // Рядків за фрагмент
final int MAX_CHUNKS = 10;      // Максимум релевантних фрагментів
int headerSize = 100;           // Рядків заголовку
int contextBefore = 3;          // Рядків до релевантного
int contextAfter = 4;           // Рядків після релевантного
```

#### Для середніх файлів (`enhancedSearch`):
```java
final int MAX_RESULTS = 20;     // Максимум релевантних секцій
int headerSize = 50;            // Рядків заголовку
int contextBefore = 10;         // Рядків до релевантного
int contextAfter = 11;          // Рядків після релевантного
```

#### Для малих файлів (`originalSearch`):
```java
int headerLines = 50;           // Рядків заголовку
int contextBefore = 5;          // Рядків до релевантного
int contextAfter = 6;           // Рядків після релевантного
int additionalLines = 2000;     // Fallback рядків
```

## Приклад роботи

### 📝 **Запит:** `!а містер сайфер`

**Файл:** 100,414 рядків

**Процес:**
1. **Заголовок:** рядки 1-100 (завжди включаються)
2. **Пошук по фрагментах:** 1000 рядків за раз
3. **Знайдено релевантні фрагменти:**
   - Фрагмент 15: рядки 15001-16000 (містить "сайфер")
   - Фрагмент 45: рядки 45001-46000 (містить "містер")
4. **Контекст:** 7 рядків навколо кожного знайденого рядка

**Результат для Gemini:**
- Заголовок: 100 рядків
- Фрагмент 15: ~7 рядків контексту
- Фрагмент 45: ~7 рядків контексту
- Загалом: ~114 рядків з 100,414 (0.11%)

## Переваги системи

### ✅ **Ефективність:**
- Аналізує тільки 10% великого файлу
- Завжди включає важливий заголовок
- Знаходить релевантну інформацію швидко

### ✅ **Точність:**
- Зберігає контекст навколо знайденої інформації
- Уникає дублювання результатів
- Fallback до зразка якщо нічого не знайдено

### ✅ **Масштабованість:**
- Працює з файлами будь-якого розміру
- Автоматично вибирає оптимальну стратегію
- Не перевищує ліміти Gemini API

**Висновок:** Бот ефективно аналізує великі файли, обробляючи тільки релевантні частини, але завжди знаходить потрібну інформацію!
