package com.example.gemini;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

public class ServerMapReader {
    
    private static final String MAP_API_URL = "http://mc.kartoplyaniy.space:40029/up/world/world/";
    
    /**
     * Player data class
     */
    public static class PlayerData {
        public String name;
        public String account;
        public double x, y, z;
        public double health;
        public int armor;
        public String world;
        
        public PlayerData(String name, String account, double x, double y, double z, double health, int armor, String world) {
            this.name = name;
            this.account = account;
            this.x = x;
            this.y = y;
            this.z = z;
            this.health = health;
            this.armor = armor;
            this.world = world;
        }
        
        @Override
        public String toString() {
            return String.format("%s (x:%.0f, y:%.0f, z:%.0f, HP:%.1f, броня:%d)",
                name, x, y, z, health, armor);
        }

        /**
         * Calculate 3D distance to another player
         */
        public double distanceTo(PlayerData other) {
            double dx = this.x - other.x;
            double dy = this.y - other.y;
            double dz = this.z - other.z;
            return Math.sqrt(dx * dx + dy * dy + dz * dz);
        }

        /**
         * Calculate 2D distance (ignoring Y coordinate) to another player
         */
        public double distance2DTo(PlayerData other) {
            double dx = this.x - other.x;
            double dz = this.z - other.z;
            return Math.sqrt(dx * dx + dz * dz);
        }

        /**
         * Calculate distance to specific coordinates
         */
        public double distanceTo(double targetX, double targetY, double targetZ) {
            double dx = this.x - targetX;
            double dy = this.y - targetY;
            double dz = this.z - targetZ;
            return Math.sqrt(dx * dx + dy * dy + dz * dz);
        }

        /**
         * Calculate 2D distance to specific coordinates
         */
        public double distance2DTo(double targetX, double targetZ) {
            double dx = this.x - targetX;
            double dz = this.z - targetZ;
            return Math.sqrt(dx * dx + dz * dz);
        }
    }
    
    /**
     * Server map data class
     */
    public static class ServerMapData {
        public int currentCount;
        public boolean hasStorm;
        public boolean isThundering;
        public long serverTime;
        public List<PlayerData> players;
        
        public ServerMapData() {
            this.players = new ArrayList<>();
        }
    }
    
    /**
     * Get current server map data
     */
    public static ServerMapData getServerMapData() {
        try {
            HttpClient client = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(10))
                .build();
            
            HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(MAP_API_URL))
                .timeout(Duration.ofSeconds(15))
                .header("User-Agent", "Minecraft-Gemini-Bot/1.0")
                .GET()
                .build();
            
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            
            if (response.statusCode() != 200) {
                System.err.println("[Server Map] HTTP error: " + response.statusCode());
                return null;
            }
            
            String responseBody = response.body();
            return parseMapData(responseBody);
            
        } catch (IOException e) {
            System.err.println("[Server Map] IOException: " + e.getMessage());
            return null;
        } catch (InterruptedException e) {
            System.err.println("[Server Map] InterruptedException: " + e.getMessage());
            return null;
        } catch (Exception e) {
            System.err.println("[Server Map] Unexpected error: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * Parse JSON map data
     */
    private static ServerMapData parseMapData(String jsonData) {
        try {
            JsonObject json = JsonParser.parseString(jsonData).getAsJsonObject();
            ServerMapData mapData = new ServerMapData();
            
            // Parse basic server info
            mapData.currentCount = json.has("currentcount") ? json.get("currentcount").getAsInt() : 0;
            mapData.hasStorm = json.has("hasStorm") ? json.get("hasStorm").getAsBoolean() : false;
            mapData.isThundering = json.has("isThundering") ? json.get("isThundering").getAsBoolean() : false;
            mapData.serverTime = json.has("servertime") ? json.get("servertime").getAsLong() : 0;
            
            // Parse players
            if (json.has("players") && json.get("players").isJsonArray()) {
                JsonArray playersArray = json.getAsJsonArray("players");
                
                for (int i = 0; i < playersArray.size(); i++) {
                    JsonObject playerObj = playersArray.get(i).getAsJsonObject();
                    
                    String name = playerObj.has("name") ? playerObj.get("name").getAsString() : "Unknown";
                    String account = playerObj.has("account") ? playerObj.get("account").getAsString() : name;
                    double x = playerObj.has("x") ? playerObj.get("x").getAsDouble() : 0.0;
                    double y = playerObj.has("y") ? playerObj.get("y").getAsDouble() : 0.0;
                    double z = playerObj.has("z") ? playerObj.get("z").getAsDouble() : 0.0;
                    double health = playerObj.has("health") ? playerObj.get("health").getAsDouble() : 20.0;
                    int armor = playerObj.has("armor") ? playerObj.get("armor").getAsInt() : 0;
                    String world = playerObj.has("world") ? playerObj.get("world").getAsString() : "world";
                    
                    PlayerData player = new PlayerData(name, account, x, y, z, health, armor, world);
                    mapData.players.add(player);
                }
            }

            return mapData;
            
        } catch (Exception e) {
            System.err.println("[Server Map] Error parsing JSON: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * Get formatted server status for AI context
     */
    public static String getServerStatusContext() {
        ServerMapData mapData = getServerMapData();
        
        if (mapData == null) {
            return "Дані карти сервера недоступні";
        }
        
        StringBuilder context = new StringBuilder();
        context.append("ПОТОЧНИЙ СТАН СЕРВЕРА:\n");
        context.append("Гравців онлайн: ").append(mapData.currentCount).append("\n");
        context.append("Погода: ");
        if (mapData.hasStorm || mapData.isThundering) {
            context.append("Гроза");
        } else {
            context.append("Ясно");
        }
        context.append("\n");
        context.append("Час сервера: ").append(mapData.serverTime).append("\n\n");
        
        if (!mapData.players.isEmpty()) {
            context.append("ГРАВЦІ ОНЛАЙН:\n");
            for (PlayerData player : mapData.players) {
                context.append("- ").append(player.toString()).append("\n");
            }
        }
        
        return context.toString();
    }
    
    /**
     * Check if query is related to server map/online players
     */
    public static boolean isMapRelatedQuery(String query) {
        String lowerQuery = query.toLowerCase();
        
        String[] mapKeywords = {
            "онлайн", "гравці", "гравець", "хто грає", "хто на сервері",
            "координати", "позиція", "де знаходиться", "здоров'я", "броня",
            "погода", "гроза", "дощ", "час сервера", "скільки гравців",
            "список гравців", "хто в грі", "статус сервера", "найближчий",
            "відстань", "далеко", "близько", "поруч", "біля"
        };
        
        for (String keyword : mapKeywords) {
            if (lowerQuery.contains(keyword)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Find closest player to a given player
     */
    public static PlayerData findClosestPlayer(String playerName, ServerMapData mapData) {
        if (mapData == null || mapData.players.isEmpty()) {
            return null;
        }

        PlayerData targetPlayer = null;
        for (PlayerData player : mapData.players) {
            if (player.name.equalsIgnoreCase(playerName) || player.account.equalsIgnoreCase(playerName)) {
                targetPlayer = player;
                break;
            }
        }

        if (targetPlayer == null) {
            return null;
        }

        PlayerData closest = null;
        double minDistance = Double.MAX_VALUE;

        for (PlayerData player : mapData.players) {
            if (!player.name.equals(targetPlayer.name)) {
                double distance = targetPlayer.distance2DTo(player);
                if (distance < minDistance) {
                    minDistance = distance;
                    closest = player;
                }
            }
        }

        return closest;
    }

    /**
     * Find players within a certain distance of coordinates
     */
    public static List<PlayerData> findPlayersNear(double x, double z, double maxDistance, ServerMapData mapData) {
        List<PlayerData> nearbyPlayers = new ArrayList<>();

        if (mapData == null || mapData.players.isEmpty()) {
            return nearbyPlayers;
        }

        for (PlayerData player : mapData.players) {
            double distance = player.distance2DTo(x, z);
            if (distance <= maxDistance) {
                nearbyPlayers.add(player);
            }
        }

        return nearbyPlayers;
    }

    /**
     * Get enhanced server status with distance calculations
     */
    public static String getEnhancedServerStatusContext(String queryPlayerName) {
        ServerMapData mapData = getServerMapData();

        if (mapData == null) {
            return "Дані карти сервера недоступні";
        }

        StringBuilder context = new StringBuilder();
        context.append("ПОТОЧНИЙ СТАН СЕРВЕРА:\n");
        context.append("Гравців онлайн: ").append(mapData.currentCount).append("\n");
        context.append("Погода: ");
        if (mapData.hasStorm || mapData.isThundering) {
            context.append("Гроза");
        } else {
            context.append("Ясно");
        }
        context.append("\n");
        context.append("Час сервера: ").append(mapData.serverTime).append("\n\n");

        if (!mapData.players.isEmpty()) {
            context.append("ГРАВЦІ ОНЛАЙН:\n");
            for (PlayerData player : mapData.players) {
                context.append("- ").append(player.toString()).append("\n");
            }

            // Add distance analysis if specific player mentioned
            if (queryPlayerName != null && !queryPlayerName.isEmpty()) {
                PlayerData queryPlayer = null;
                for (PlayerData player : mapData.players) {
                    if (player.name.equalsIgnoreCase(queryPlayerName) || player.account.equalsIgnoreCase(queryPlayerName)) {
                        queryPlayer = player;
                        break;
                    }
                }

                if (queryPlayer != null) {
                    context.append("\nАНАЛІЗ ВІДСТАНЕЙ ДЛЯ ").append(queryPlayer.name).append(":\n");

                    // Find closest player
                    PlayerData closest = findClosestPlayer(queryPlayer.name, mapData);
                    if (closest != null) {
                        double closestDistance = queryPlayer.distance2DTo(closest);
                        context.append("- НАЙБЛИЖЧИЙ: ").append(closest.name)
                               .append(" (").append(String.format("%.1f", closestDistance))
                               .append(" блоків)\n");
                    }

                    // List all distances
                    for (PlayerData otherPlayer : mapData.players) {
                        if (!otherPlayer.name.equals(queryPlayer.name)) {
                            double distance = queryPlayer.distance2DTo(otherPlayer);
                            context.append("- До ").append(otherPlayer.name)
                                   .append(": ").append(String.format("%.1f", distance))
                                   .append(" блоків\n");
                        }
                    }
                }
            }
        }

        return context.toString();
    }
}
