package com.example.gemini;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;

public class SimpleGeminiClient {

    private static final String API_KEY = "AIzaSyBi11VCTntbs_C15KpbcYbP1RLjEzEDnpM";
    private static final String API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent";

    public static String getResponse(String prompt) {
        System.out.println("[Simple Gemini API] Starting API call with prompt: " + prompt);
        
        try {
            // Create HTTP client with timeout
            HttpClient client = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(30))
                .build();

            // Build request JSON
            JsonObject requestBody = new JsonObject();
            JsonArray contents = new JsonArray();
            JsonObject content = new JsonObject();
            JsonArray parts = new JsonArray();
            JsonObject part = new JsonObject();
            // Prompt with STRICT 128 character limit
            String fullPrompt = "You have access to Google Search. Please search for current information to answer this question accurately. CRITICAL: Always respond in Ukrainian language only. Your response MUST be under 90 characters maximum (will have 'Gemini:' prefix added, TOTAL MUST BE ≤128 chars). Do NOT include 'Gemini:' in your response. Be extremely concise. Question: " + prompt;
            part.addProperty("text", fullPrompt);
            parts.add(part);
            content.add("parts", parts);
            contents.add(content);
            requestBody.add("contents", contents);

            // Add Google Search grounding tool (simplified approach)
            JsonArray tools = new JsonArray();
            JsonObject tool = new JsonObject();
            JsonObject googleSearch = new JsonObject();
            tool.add("google_search", googleSearch);
            tools.add(tool);
            requestBody.add("tools", tools);

            System.out.println("[Simple Gemini API] Request JSON with Google Search: " + requestBody.toString());

            // Build request URL with API key
            String fullUrl = API_URL + "?key=" + API_KEY;
            System.out.println("[Simple Gemini API] Making request to Gemini API...");

            // Create HTTP request
            HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(fullUrl))
                .timeout(Duration.ofSeconds(45))
                .header("Content-Type", "application/json")
                .POST(HttpRequest.BodyPublishers.ofString(requestBody.toString()))
                .build();

            System.out.println("[Simple Gemini API] Executing request...");
            
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            System.out.println("[Simple Gemini API] Request completed!");

            String responseBody = response.body();
            System.out.println("[Simple Gemini API] Response status: " + response.statusCode());
            System.out.println("[Simple Gemini API] Full response body: " + responseBody);

            if (response.statusCode() != 200) {
                System.err.println("[Simple Gemini API] API returned error status: " + response.statusCode());
                System.err.println("[Simple Gemini API] Error response body: " + responseBody);

                // Try fallback for 400 errors (might be Google Search issue)
                if (response.statusCode() == 400) {
                    System.out.println("[Simple Gemini API] Trying fallback without Google Search due to 400 error...");
                    return getResponseWithoutSearch(prompt);
                }

                return "API Error: Status " + response.statusCode();
            }

            // Parse response
            JsonObject responseJson = JsonParser.parseString(responseBody).getAsJsonObject();
            
            if (responseJson.has("candidates") && responseJson.getAsJsonArray("candidates").size() > 0) {
                JsonObject candidate = responseJson.getAsJsonArray("candidates").get(0).getAsJsonObject();
                if (candidate.has("content")) {
                    JsonObject content_obj = candidate.getAsJsonObject("content");
                    if (content_obj.has("parts") && content_obj.getAsJsonArray("parts").size() > 0) {
                        JsonObject part_obj = content_obj.getAsJsonArray("parts").get(0).getAsJsonObject();
                        if (part_obj.has("text")) {
                            String text = part_obj.get("text").getAsString();
                            System.out.println("[Simple Gemini API] Extracted text: " + text);

                            // Log grounding metadata if available
                            logGroundingMetadata(candidate);
                            return text;
                        }
                    }
                }
            }

            System.err.println("[Simple Gemini API] Could not extract text from response");
            return "Could not parse API response";

        } catch (IOException e) {
            System.err.println("[Simple Gemini API] IOException: " + e.getMessage());
            e.printStackTrace();

            // Try fallback if timeout occurred
            if (e.getMessage().contains("timed out") || e.getMessage().contains("timeout")) {
                System.out.println("[Simple Gemini API] Trying fallback...");
                return getResponseWithoutSearch(prompt);
            }

            return "Network error: " + e.getMessage();
        } catch (InterruptedException e) {
            System.err.println("[Simple Gemini API] InterruptedException: " + e.getMessage());
            e.printStackTrace();
            return "Request interrupted: " + e.getMessage();
        } catch (Exception e) {
            System.err.println("[Simple Gemini API] Unexpected error: " + e.getMessage());
            e.printStackTrace();
            return "Unexpected error: " + e.getMessage();
        }
    }



    /**
     * Fallback method to get response without Google Search
     */
    private static String getResponseWithoutSearch(String prompt) {
        try {
            System.out.println("[Simple Gemini API] Making fallback request...");

            HttpClient client = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(20))
                .build();

            // Build simple request JSON without tools
            JsonObject requestBody = new JsonObject();
            JsonArray contents = new JsonArray();
            JsonObject content = new JsonObject();
            JsonArray parts = new JsonArray();
            JsonObject part = new JsonObject();

            String fallbackPrompt = "CRITICAL: Always respond in Ukrainian language only. Your response MUST be under 90 characters maximum (will have 'Gemini:' prefix added, TOTAL MUST BE ≤128 chars). Do NOT include 'Gemini:' in your response. Be extremely concise. Question: " + prompt;
            part.addProperty("text", fallbackPrompt);

            parts.add(part);
            content.add("parts", parts);
            contents.add(content);
            requestBody.add("contents", contents);

            String fullUrl = API_URL + "?key=" + API_KEY;

            HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(fullUrl))
                .timeout(Duration.ofSeconds(30))
                .header("Content-Type", "application/json")
                .POST(HttpRequest.BodyPublishers.ofString(requestBody.toString()))
                .build();

            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            String responseBody = response.body();

            System.out.println("[Simple Gemini API] Fallback response status: " + response.statusCode());

            if (response.statusCode() != 200) {
                return "API Error (fallback): Status " + response.statusCode();
            }

            // Parse response
            JsonObject responseJson = JsonParser.parseString(responseBody).getAsJsonObject();

            if (responseJson.has("candidates") && responseJson.getAsJsonArray("candidates").size() > 0) {
                JsonObject candidate = responseJson.getAsJsonArray("candidates").get(0).getAsJsonObject();
                if (candidate.has("content")) {
                    JsonObject content_obj = candidate.getAsJsonObject("content");
                    if (content_obj.has("parts") && content_obj.getAsJsonArray("parts").size() > 0) {
                        JsonObject part_obj = content_obj.getAsJsonArray("parts").get(0).getAsJsonObject();
                        if (part_obj.has("text")) {
                            String text = part_obj.get("text").getAsString();
                            System.out.println("[Simple Gemini API] Fallback successful: " + text);
                            return text;
                        }
                    }
                }
            }

            return "Could not parse fallback response";

        } catch (Exception e) {
            System.err.println("[Simple Gemini API] Fallback also failed: " + e.getMessage());
            return "Both main and fallback requests failed";
        }
    }

    /**
     * Log grounding metadata for debugging purposes
     */
    private static void logGroundingMetadata(JsonObject candidate) {
        try {
            if (!candidate.has("groundingMetadata")) {
                System.out.println("[Simple Gemini API] No grounding metadata - answered from model knowledge");
                return;
            }

            JsonObject groundingMetadata = candidate.getAsJsonObject("groundingMetadata");
            System.out.println("[Simple Gemini API] Grounding metadata found");

            // Log search queries used
            if (groundingMetadata.has("webSearchQueries")) {
                JsonArray queries = groundingMetadata.getAsJsonArray("webSearchQueries");
                System.out.println("[Simple Gemini API] Search queries: " + queries.toString());
            }

            // Log grounding chunks (sources)
            if (groundingMetadata.has("groundingChunks")) {
                JsonArray chunks = groundingMetadata.getAsJsonArray("groundingChunks");
                System.out.println("[Simple Gemini API] Found " + chunks.size() + " grounding sources");

                for (int i = 0; i < Math.min(chunks.size(), 3); i++) { // Log first 3 sources
                    JsonObject chunk = chunks.get(i).getAsJsonObject();
                    if (chunk.has("web")) {
                        JsonObject web = chunk.getAsJsonObject("web");
                        if (web.has("title") && web.has("uri")) {
                            String title = web.get("title").getAsString();
                            String uri = web.get("uri").getAsString();
                            System.out.println("[Simple Gemini API] Source " + (i+1) + ": " + title + " (" + uri + ")");
                        }
                    }
                }
            }

        } catch (Exception e) {
            System.err.println("[Simple Gemini API] Error processing grounding metadata: " + e.getMessage());
        }
    }
}
