# Виправлення помилки парсингу чату

## Проблема
Мод крашився з помилкою `StringIndexOutOfBoundsException` при обробці власних повідомлень бота:

```
java.lang.StringIndexOutOfBoundsException: Range [17, 7) out of bounds for length 127
	at java.base/java.lang.String.substring(String.java:2834)
	at knot/com.example.gemini.Gemini.handleChatMessage(Gemini.java:66)
```

**Причина:** Повідомлення бота містило символи `>>> /rtp <<<`, які конфліктували з логікою парсингу формату `<player> message`.

## Проблемне повідомлення
```
">>> /rtp <<<  Команда /rtp використовувалась гравцями кілька разів. Це може свідчити про її функціональність на сервері."
```

Код намагався знайти `<` та `>` для парсингу імені гравця, але:
- `text.indexOf("<")` повертав 17 (позиція в "<<<")
- `text.indexOf(">")` повертав 7 (позиція в ">>>")
- Результат: `substring(17, 7)` - неможливий діапазон!

## Рішення

### 1. **Покращена перевірка позицій**
```java
// БУЛО:
senderName = text.substring(text.indexOf("<") + 1, text.indexOf(">"));

// СТАЛО:
int startBracket = text.indexOf("<");
int endBracket = text.indexOf(">");
int index = text.indexOf("> ");

// Ensure brackets are in correct order and exist
if (startBracket != -1 && endBracket != -1 && startBracket < endBracket && index != -1) {
    senderName = text.substring(startBracket + 1, endBracket);
    actualMessage = text.substring(index + 2);
}
```

### 2. **Додаткові перевірки для формату "player:"**
```java
// БУЛО:
if (index != -1) {

// СТАЛО:
if (index != -1 && index > 0) {
```

### 3. **Запобігання обробці власних повідомлень**
```java
// Skip if this is our own bot message
if (senderName != null && (senderName.equals("Amazo") || senderName.equals("Кікерша"))) {
    System.out.println("[Chat Parser] Skipping own bot message from: " + senderName);
    return;
}
```

### 4. **Детальне логування для діагностики**
```java
System.out.println("[Chat Parser] Raw message: " + text);
System.out.println("[Chat Parser] Found '> ' pattern. Start: " + startBracket + ", End: " + endBracket + ", Index: " + index);
System.out.println("[Chat Parser] Parsed <player> format. Sender: " + senderName + ", Message: " + actualMessage);
```

## Логіка парсингу після виправлення

### 📝 **Формат 1: `<player> message`**
```java
if (text.contains("> ")) {
    int startBracket = text.indexOf("<");
    int endBracket = text.indexOf(">");
    int index = text.indexOf("> ");
    
    // Перевірка правильності позицій
    if (startBracket != -1 && endBracket != -1 && 
        startBracket < endBracket && index != -1) {
        // Безпечний парсинг
        senderName = text.substring(startBracket + 1, endBracket);
        actualMessage = text.substring(index + 2);
    }
}
```

### 📝 **Формат 2: `player: message`**
```java
else if (text.contains(": ")) {
    int index = text.indexOf(": ");
    
    // Перевірка валідності позиції
    if (index != -1 && index > 0) {
        senderName = text.substring(0, index);
        actualMessage = text.substring(index + 2);
    }
}
```

### 📝 **Фільтрація власних повідомлень**
```java
// Пропускаємо власні повідомлення бота
if (senderName != null && (senderName.equals("Amazo") || senderName.equals("Кікерша"))) {
    return; // Не обробляємо
}
```

## Приклади роботи

### ✅ **Валідні повідомлення:**
```
"<Player123> !a що таке /rtp"
→ Sender: "Player123", Message: "!a що таке /rtp"

"Player456: !а хто такий сайфер"
→ Sender: "Player456", Message: "!а хто такий сайфер"
```

### ❌ **Проблемні повідомлення (тепер безпечні):**
```
">>> /rtp <<< Команда /rtp використовувалась..."
→ Неправильний порядок < та >, пропускається

"Amazo: Відповідь бота на запитання"
→ Власне повідомлення бота, пропускається
```

## Логування

### 📝 **Нові повідомлення в консолі:**
```
[Chat Parser] Raw message: >>> /rtp <<< Команда /rtp використовувалась...
[Chat Parser] Found '> ' pattern. Start: 17, End: 7, Index: 4
[Chat Parser] Invalid bracket positions, skipping <player> format
[Chat Parser] Found ': ' pattern at index: -1
[Chat Parser] Invalid ': ' position, skipping player: format

[Chat Parser] Raw message: <Player123> !a що таке /rtp
[Chat Parser] Found '> ' pattern. Start: 0, End: 10, Index: 10
[Chat Parser] Parsed <player> format. Sender: Player123, Message: !a що таке /rtp
```

## Переваги виправлення

### ✅ **Стабільність:**
- Немає крашів через неправильні індекси
- Безпечна обробка всіх форматів повідомлень
- Захист від власних повідомлень бота

### ✅ **Діагностика:**
- Детальне логування процесу парсингу
- Легко знайти причину проблем
- Зрозумілі повідомлення про помилки

### ✅ **Надійність:**
- Перевірка всіх умов перед парсингом
- Fallback для невалідних форматів
- Запобігання зациклювання на власних повідомленнях

## Тестування

### 🧪 **Тестові випадки:**
1. **Нормальні повідомлення:** `<Player> !a запит` ✅
2. **Формат з двокрапкою:** `Player: !а запит` ✅
3. **Власні повідомлення бота:** `Amazo: відповідь` ✅ (пропускається)
4. **Повідомлення з символами:** `>>> текст <<<` ✅ (безпечно)
5. **Порожні повідомлення:** `` ✅ (ігнорується)

## Результат

**Мод тепер стабільно обробляє всі типи чат-повідомлень без крашів і не реагує на власні повідомлення!**

🛡️ **Захист від:** StringIndexOutOfBoundsException, зациклювання, неправильного парсингу
