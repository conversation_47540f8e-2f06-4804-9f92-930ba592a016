# Підтримка великих файлів

## Проблема
Файл з 100,414 рядків перевищує ліміти Gemini API і не може бути оброблений повністю.

## Рішення: Розумна система читання по частинах

### 🎯 Стратегія обробки за розміром файлу:

#### 1. **Малі файли (<10,000 рядків)**
- **Метод:** `originalSearch()`
- **Логіка:** Читає весь файл + контекст навколо релевантних рядків
- **Контекст:** 5 рядків до і після релевантного рядка

#### 2. **Середні файли (10,000-50,000 рядків)**
- **Метод:** `enhancedSearch()`
- **Логіка:** Заголовок + до 20 релевантних секцій
- **Контекст:** 10 рядків до і після релевантного рядка
- **Оптимізація:** Пропускає перекриваючі результати

#### 3. **Великі файли (>50,000 рядків)**
- **Метод:** `smartChunkSearch()`
- **Логіка:** Заголовок + обробка по 1000 рядків за раз
- **Ліміт:** Максимум 10 релевантних фрагментів
- **Контекст:** 3 рядки до і після релевантного рядка

## Як працює Smart Chunk Search

### 📊 Для файлу з 100,414 рядків:

```
1. Заголовок (перші 100 рядків) - завжди включається
2. Обробка по фрагментах:
   - Фрагмент 1: рядки 101-1100
   - Фрагмент 2: рядки 1101-2100
   - ...
   - Фрагмент N: рядки 99101-100100
3. Для кожного фрагменту:
   - Пошук ключових слів з запиту
   - Якщо знайдено - включити контекст
   - Максимум 10 релевантних фрагментів
```

### 🔍 Приклад обробки запиту "містер сайфер":

```
=== ЗАГОЛОВОК ФАЙЛУ ===
[перші 100 рядків файлу]
=== КІНЕЦЬ ЗАГОЛОВКУ ===

=== РЕЛЕВАНТНИЙ ФРАГМЕНТ (рядки 15234-16234) ===
...контекст...
Містер Сайфер створив світ з яйця
...контекст...
=== КІНЕЦЬ ФРАГМЕНТУ ===

=== РЕЛЕВАНТНИЙ ФРАГМЕНТ (рядки 45678-46678) ===
...контекст...
Сайфер заснував перше поселення
...контекст...
=== КІНЕЦЬ ФРАГМЕНТУ ===
```

## Переваги системи

### ✅ **Ефективність:**
- Обробляє файли будь-якого розміру
- Не перевищує ліміти Gemini API
- Знаходить релевантну інформацію швидко

### ✅ **Точність:**
- Завжди включає заголовок файлу
- Зберігає контекст навколо знайденої інформації
- Уникає дублювання результатів

### ✅ **Гнучкість:**
- Автоматично вибирає стратегію за розміром
- Налаштовувані параметри (розмір фрагментів, кількість результатів)
- Fallback до зразка, якщо нічого не знайдено

## Логування

### 📝 Що ви побачите в консолі:

```
[Smart Filter] Processing 100414 lines, query: містер сайфер
[Smart Chunk] Processing 100414 lines in chunks of 1000
[Smart Chunk] Found relevant info in chunk 15234-16234
[Smart Chunk] Found relevant info in chunk 45678-46678
[Smart Chunk] Final result: 8543 characters from 2 relevant chunks
```

## Налаштування

### 🔧 Параметри, які можна змінити:

```java
// У методі smartChunkSearch():
final int CHUNK_SIZE = 1000;    // Розмір фрагменту
final int MAX_CHUNKS = 10;      // Максимум фрагментів
int headerSize = 100;           // Розмір заголовку

// У методі enhancedSearch():
final int MAX_RESULTS = 20;     // Максимум результатів
int headerSize = 50;            // Розмір заголовку
```

## Результат

### 🎯 **Для файлу з 100,414 рядків:**
- ✅ Читається без помилок
- ✅ Знаходить релевантну інформацію
- ✅ Не перевищує ліміти API
- ✅ Зберігає контекст та структуру
- ✅ Швидка обробка запитів

### 📊 **Типовий результат:**
- Заголовок: ~100 рядків
- Релевантні фрагменти: 2-10 фрагментів по ~50-100 рядків
- Загальний розмір: 5,000-15,000 символів (в межах лімітів)

**Висновок:** Система дозволяє ефективно працювати з файлами будь-якого розміру, включаючи файли з 100k+ рядків!
