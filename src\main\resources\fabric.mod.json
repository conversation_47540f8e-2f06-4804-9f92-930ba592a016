{"schemaVersion": 1, "id": "gemini", "version": "${version}", "name": "Gemini", "description": "Minecraft Fabric mod that integrates with Gemini API for server assistance", "authors": ["Server Assistant"], "license": "CC0-1.0", "icon": "assets/gemini/icon.png", "environment": "client", "entrypoints": {"client": ["com.example.gemini.Gemini"]}, "mixins": ["gemini.mixins.json"], "depends": {"fabricloader": ">=0.16.14", "minecraft": "~1.21.5", "java": ">=21", "fabric-api": "*"}}