package com.example.gemini;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;

public class NewGeminiClient {
    
    private static final String API_KEY = "AIzaSyBi11VCTntbs_C15KpbcYbP1RLjEzEDnpM";
    private static final String MODEL = "gemini-2.5-flash-lite-preview-06-17";
    private static final String API_URL = "https://generativelanguage.googleapis.com/v1beta/models/" + MODEL + ":generateContent";
    
    public static String getResponse(String prompt) {
        System.out.println("[Server Assistant] Starting API call with prompt: " + prompt);
        System.out.println("[Server Assistant] Searching for answer in all server files...");

        // Try multiple attempts to ensure complete responses within limits
        // Start with max limit and retry if response is incomplete
        int maxAttempts = 3;

        for (int attempt = 0; attempt < maxAttempts; attempt++) {
            System.out.println("[Server Assistant] Attempt " + (attempt + 1) + " of " + maxAttempts);

            try {
                HttpClient client = HttpClient.newBuilder()
                    .build();

                // Create request body with increased limits (150 tokens max for complete responses)
                JsonObject requestBody = createRequestBody(prompt, attempt);

                System.out.println("[Server Assistant] Request JSON created for attempt " + (attempt + 1));

                // Build request URL with API key
                String fullUrl = API_URL + "?key=" + API_KEY;

                // Create HTTP request
                HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(fullUrl))
                    .timeout(Duration.ofSeconds(30))
                    .header("Content-Type", "application/json")
                    .POST(HttpRequest.BodyPublishers.ofString(requestBody.toString()))
                    .build();

                System.out.println("[Server Assistant] Making request to Gemini API...");

                HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
                String responseBody = response.body();

                System.out.println("[Server Assistant] Response status: " + response.statusCode());

                if (response.statusCode() != 200) {
                    System.err.println("[Server Assistant] API error: " + response.statusCode());
                    continue; // Try next attempt
                }

                // Parse response
                String extractedText = extractTextFromResponse(responseBody);

                if (extractedText == null || extractedText.isEmpty()) {
                    System.out.println("[Server Assistant] Empty response, trying next attempt...");
                    continue; // Try next attempt
                }

                System.out.println("[Server Assistant] Extracted text: " + extractedText);
                System.out.println("[Server Assistant] Response length: " + extractedText.length());

                // Check if response is complete (ends with proper punctuation and fits in limit)
                if (isResponseComplete(extractedText)) {
                    System.out.println("[Server Assistant] ✓ Response is complete and ready to send");

                    // Smart truncation for Minecraft chat if needed
                    if (extractedText.length() > 256) {
                        String truncated = smartTruncate(extractedText, 256);
                        System.out.println("[Server Assistant] Truncated response from " + extractedText.length() + " to " + truncated.length() + " characters");
                        return truncated;
                    }

                    return extractedText;
                } else {
                    System.out.println("[Server Assistant] ⚠ Response appears incomplete, trying with fewer tokens...");
                    // Continue to next attempt with fewer tokens
                }

            } catch (IOException e) {
                System.err.println("[Server Assistant] IOException on attempt " + (attempt + 1) + ": " + e.getMessage());
                if (attempt == maxAttempts - 1) {
                    return "Помилка мережі. Спробуйте пізніше.";
                }
            } catch (InterruptedException e) {
                System.err.println("[Server Assistant] Request interrupted on attempt " + (attempt + 1) + ": " + e.getMessage());
                if (attempt == maxAttempts - 1) {
                    return "Запит перервано. Спробуйте ще раз.";
                }
            } catch (Exception e) {
                System.err.println("[Server Assistant] Unexpected error on attempt " + (attempt + 1) + ": " + e.getMessage());
                if (attempt == maxAttempts - 1) {
                    e.printStackTrace();
                    return "Помилка помічника сервера.";
                }
            }
        }

        // If all attempts failed
        System.err.println("[Server Assistant] All attempts failed to generate complete response");
        return "Немає інформації про це питання сервера";
    }

    /**
     * Check if response appears complete (ends with proper punctuation and fits in character limit)
     */
    private static boolean isResponseComplete(String response) {
        if (response == null || response.isEmpty()) {
            return false;
        }

        // Check character limit - allow responses up to 300 characters (will be truncated if needed)
        if (response.length() > 300) {
            System.out.println("[Server Assistant] Response too long: " + response.length() + " characters (max 300)");
            return false;
        }

        // Check if ends with proper punctuation and is not truncated
        String trimmed = response.trim();
        if (trimmed.isEmpty()) {
            return false;
        }

        char lastChar = trimmed.charAt(trimmed.length() - 1);
        boolean endsWithPunctuation = (lastChar == '.' || lastChar == '!' || lastChar == '?' || lastChar == ':');

        if (!endsWithPunctuation) {
            System.out.println("[Server Assistant] Response doesn't end with proper punctuation: '" + lastChar + "'");
            return false;
        }

        // Check for common signs of truncation
        String lowerTrimmed = trimmed.toLowerCase();
        String[] truncationSigns = {
            " в...", " та...", " і...", " або...", " що...", " як...", " на...", " з...", " до...",
            " рангі в", " участь у", " взаємодію з", " обговорює з", " спілкується про"
        };

        for (String sign : truncationSigns) {
            if (lowerTrimmed.endsWith(sign.trim())) {
                System.out.println("[Server Assistant] Response appears truncated (ends with: '" + sign.trim() + "')");
                return false;
            }
        }

        // Less strict check: only avoid obviously incomplete responses
        String[] obviouslyIncomplete = {" і", " та", " або", " але", " що", " як", " до", " від", " на", " в", " з", " у", " за", " про", " для", " під", " над", " між", " через", " після", " перед"};
        String lowerResponse = trimmed.toLowerCase();

        for (String incomplete : obviouslyIncomplete) {
            if (lowerResponse.endsWith(incomplete + ".") || lowerResponse.endsWith(incomplete + "!") || lowerResponse.endsWith(incomplete + "?")) {
                System.out.println("[Server Assistant] Response ends with obviously incomplete pattern: '" + incomplete + "'");
                return false;
            }
        }

        // Accept responses that are reasonably complete
        System.out.println("[Server Assistant] ✓ Response appears complete: " + trimmed.length() + " chars, ends with '" + lastChar + "'");
        return true;
    }
    
    /**
     * Create request body inspired by SDK structure
     */
    private static JsonObject createRequestBody(String prompt, int attemptNumber) {
        // Get server information for context
        String fullServerContext = ServerInfoReader.getServerContext();
        System.out.println("[Server Assistant] Full server context length: " + fullServerContext.length());

        // Enhanced context filtering for better accuracy
        String serverContext = filterRelevantContext(fullServerContext, prompt);
        System.out.println("[Server Assistant] Filtered context length: " + serverContext.length());

        // Context validation - ensure filtering doesn't lose important information
        System.out.println("[Server Assistant] Context filtering completed successfully");

        // Check if query is related to server map/online players
        String mapContext = "";
        if (ServerMapReader.isMapRelatedQuery(prompt)) {
            System.out.println("[Server Assistant] Query is map-related, fetching live server data...");
            String playerName = extractPlayerNameFromQuery(prompt);
            mapContext = ServerMapReader.getEnhancedServerStatusContext(playerName);
            System.out.println("[Server Assistant] Map context length: " + mapContext.length());
        }

        // Increased token limits to handle larger context
        int[] responseLimits = {200, 150, 120};
        int maxTokens = responseLimits[Math.min(attemptNumber, responseLimits.length - 1)];

        // Create an improved prompt with season context and better quality control
        String serverPrompt = "Ти помічник сервера Картопляний. Дай точну та коректну відповідь на основі бази знань.\n\n" +
            "КОНТЕКСТ СЕЗОНІВ:\n" +
            "- 3 сезон почався 21 червня 2025 року (ПОТОЧНИЙ СЕЗОН)\n" +
            "- Все до 21.06.2025 відноситься до попередніх сезонів (1-2 сезони)\n" +
            "- При відповіді про минулі події використовуй минулий час\n" +
            "- При відповіді про поточні речі використовуй теперішній час\n\n" +
            "ПРАВИЛА ВІДПОВІДЕЙ:\n" +
            "1. Шукай ТОЧНУ інформацію в базі знань\n" +
            "2. Якщо знайшов - дай конкретну відповідь з фактами\n" +
            "3. Якщо НЕ знайшов - скажи \"Інформація відсутня в базі знань\"\n" +
            "4. НЕ вигадуй та НЕ додавай інформацію, якої немає в базі\n" +
            "5. Відповідь має бути коректною, тактовною та до 200 символів\n" +
            "6. Відповідай українською мовою\n" +
            "7. Почни відразу з відповіді\n" +
            "8. Уникай образливих або неприйнятних формулювань\n" +
            "9. Для гравців вказуй тільки факти з бази знань\n\n" +
            "ЗАПИТ: " + prompt + "\n\n" +
            "БАЗА ЗНАНЬ СЕРВЕРА:\n" + serverContext + "\n\n" +
            (mapContext.isEmpty() ? "" : "ДАНІ КАРТИ СЕРВЕРА:\n" + mapContext + "\n\n") +
            "ВІДПОВІДЬ:";
        
        JsonObject requestBody = new JsonObject();
        
        // Create contents array (like SDK's ImmutableList<Content>)
        JsonArray contents = new JsonArray();
        JsonObject content = new JsonObject();
        content.addProperty("role", "user");
        
        // Create parts array (like SDK's ImmutableList<Part>)
        JsonArray parts = new JsonArray();
        JsonObject part = new JsonObject();
        part.addProperty("text", serverPrompt);
        parts.add(part);
        
        content.add("parts", parts);
        contents.add(content);
        requestBody.add("contents", contents);
        
        // Optimized generation config for accurate and complete responses
        JsonObject generationConfig = new JsonObject();
        generationConfig.addProperty("topP", 0.9f); // More focused for factual responses
        generationConfig.addProperty("temperature", 0.3f); // Lower temperature for more accurate, less creative responses
        generationConfig.addProperty("maxOutputTokens", maxTokens);
        generationConfig.addProperty("responseMimeType", "text/plain");
        generationConfig.addProperty("candidateCount", 1);

        requestBody.add("generationConfig", generationConfig);

        System.out.println("[Server Assistant] Using maxOutputTokens: " + maxTokens + " (attempt " + (attemptNumber + 1) + ")");
        
        return requestBody;
    }
    
    /**
     * Extract text from response (like SDK's response processing)
     */
    private static String extractTextFromResponse(String responseBody) {
        try {
            JsonObject responseJson = JsonParser.parseString(responseBody).getAsJsonObject();
            
            if (responseJson.has("candidates") && responseJson.getAsJsonArray("candidates").size() > 0) {
                JsonObject candidate = responseJson.getAsJsonArray("candidates").get(0).getAsJsonObject();
                if (candidate.has("content")) {
                    JsonObject content = candidate.getAsJsonObject("content");
                    if (content.has("parts") && content.getAsJsonArray("parts").size() > 0) {
                        JsonObject part = content.getAsJsonArray("parts").get(0).getAsJsonObject();
                        if (part.has("text")) {
                            String response = part.get("text").getAsString().trim();

                            // Enhanced validation against inappropriate and hallucinated content
                            String lowerResponse = response.toLowerCase();

                            // Block inappropriate content
                            String[] inappropriateContent = {
                                "nigger", "нігер", "негр", "чорний", "раса", "расовий",
                                "жити стане важче", "для любителів гардкору",
                                "бонус", "раса в грі"
                            };

                            for (String inappropriate : inappropriateContent) {
                                if (lowerResponse.contains(inappropriate.toLowerCase())) {
                                    System.out.println("[Validation] Blocked inappropriate content: " + inappropriate);
                                    return "Інформація відсутня в базі знань";
                                }
                            }

                            // Block hallucinated content
                            String[] obviouslyFakePhrases = {
                                "є скороченням для",
                                "це скорочення означає",
                                "означає абревіатуру",
                                "розшифровується як",
                                "це абревіатура від",
                                "раса в грі",
                                "надає бонус"
                            };

                            for (String phrase : obviouslyFakePhrases) {
                                if (lowerResponse.contains(phrase.toLowerCase())) {
                                    System.out.println("[Validation] Detected potential hallucination: " + phrase);
                                    return "Інформація відсутня в базі знань";
                                }
                            }

                            // Перевіряємо, чи відповідь в межах лімітів (але не обрізаємо примусово)
                            if (response.length() > 256) {
                                System.out.println("[New Gemini Client] Warning: Response exceeds 256 characters (" + response.length() + "), but keeping full response");
                            }

                            return response;
                        }
                    }
                }
            }
            
            System.err.println("[New Gemini Client] Could not extract text from response");
            return null;
            
        } catch (Exception e) {
            System.err.println("[New Gemini Client] Error parsing response: " + e.getMessage());
            return null;
        }
    }

    /**
     * Filter relevant context based on query keywords with improved search algorithm
     */
    private static String filterRelevantContext(String fullContext, String query) {
        String[] lines = fullContext.split("\n");
        System.out.println("[Enhanced Filter] Processing " + lines.length + " lines, query: " + query);

        // Use enhanced search algorithm that finds more relevant information
        return enhancedContextSearch(lines, query);
    }

    /**
     * Enhanced context search - improved algorithm for finding relevant information
     */
    private static String enhancedContextSearch(String[] lines, String query) {
        StringBuilder result = new StringBuilder();
        String lowerQuery = query.toLowerCase();

        // Extract key terms from query (remove common words)
        String[] commonWords = {"що", "як", "де", "коли", "чому", "хто", "про", "на", "в", "з", "до", "від", "для", "та", "і", "або", "але", "це", "той", "цей", "який", "яка", "яке", "які", "такий", "така", "таке", "такі"};
        String[] queryTerms = lowerQuery.split("\\s+");
        java.util.List<String> keyTerms = new java.util.ArrayList<>();

        for (String term : queryTerms) {
            if (term.length() > 2) {
                boolean isCommon = false;
                for (String common : commonWords) {
                    if (term.equals(common)) {
                        isCommon = true;
                        break;
                    }
                }
                if (!isCommon) {
                    keyTerms.add(term);
                }
            }
        }

        // Add season context awareness
        boolean isHistoricalQuery = lowerQuery.contains("був") || lowerQuery.contains("була") ||
                                   lowerQuery.contains("було") || lowerQuery.contains("були") ||
                                   lowerQuery.contains("колись") || lowerQuery.contains("раніше");
        System.out.println("[Enhanced Search] Historical query detected: " + isHistoricalQuery);

        System.out.println("[Enhanced Search] Key terms: " + keyTerms);

        // Special handling for player names - add original query terms for better matching
        for (String term : queryTerms) {
            if (term.length() > 3 && !keyTerms.contains(term)) {
                keyTerms.add(term);
            }
        }

        System.out.println("[Enhanced Search] Extended key terms (including player names): " + keyTerms);

        // Phase 1: Find exact phrase matches (highest priority)
        java.util.List<MatchResult> exactMatches = new java.util.ArrayList<>();
        for (int i = 0; i < lines.length; i++) {
            String line = lines[i].toLowerCase();
            if (line.contains(lowerQuery)) {
                exactMatches.add(new MatchResult(i, 100, lines[i])); // Highest score for exact matches
                System.out.println("[Enhanced Search] Found exact phrase match at line " + (i+1));
            }
        }

        // Phase 2: Find lines with multiple key terms (high priority)
        java.util.List<MatchResult> multiTermMatches = new java.util.ArrayList<>();
        for (int i = 0; i < lines.length; i++) {
            String line = lines[i].toLowerCase();
            int termCount = 0;
            for (String term : keyTerms) {
                if (line.contains(term)) {
                    termCount++;
                }
            }
            if (termCount >= 2) {
                multiTermMatches.add(new MatchResult(i, 50 + termCount * 10, lines[i]));
            }
        }

        // Phase 3: Find lines with single key terms (medium priority)
        java.util.List<MatchResult> singleTermMatches = new java.util.ArrayList<>();
        for (int i = 0; i < lines.length; i++) {
            String line = lines[i].toLowerCase();
            for (String term : keyTerms) {
                if (line.contains(term)) {
                    boolean alreadyFound = false;
                    for (MatchResult match : exactMatches) {
                        if (match.lineNumber == i) {
                            alreadyFound = true;
                            break;
                        }
                    }
                    for (MatchResult match : multiTermMatches) {
                        if (match.lineNumber == i) {
                            alreadyFound = true;
                            break;
                        }
                    }
                    if (!alreadyFound) {
                        singleTermMatches.add(new MatchResult(i, 20, lines[i]));
                        break; // Only add once per line
                    }
                }
            }
        }

        System.out.println("[Enhanced Search] Found " + exactMatches.size() + " exact matches, " +
                          multiTermMatches.size() + " multi-term matches, " +
                          singleTermMatches.size() + " single-term matches");

        // Combine all matches and sort by relevance
        java.util.List<MatchResult> allMatches = new java.util.ArrayList<>();
        allMatches.addAll(exactMatches);
        allMatches.addAll(multiTermMatches);
        allMatches.addAll(singleTermMatches);

        // Sort by relevance score (highest first)
        allMatches.sort((a, b) -> Integer.compare(b.relevanceScore, a.relevanceScore));

        // Include header with season context
        result.append("=== КОНТЕКСТ СЕЗОНІВ ===\n");
        result.append("3 СЕЗОН: почався 21 червня 2025 року (ПОТОЧНИЙ)\n");
        result.append("1-2 СЕЗОНИ: до 21 червня 2025 року (МИНУЛІ)\n");
        result.append("=== ЗАГОЛОВОК ФАЙЛУ ===\n");
        int headerSize = Math.min(50, lines.length);
        for (int i = 0; i < headerSize; i++) {
            result.append(lines[i]).append("\n");
        }
        result.append("=== КІНЕЦЬ ЗАГОЛОВКУ ===\n\n");

        // Include top matches with context - increased for better coverage
        int maxResults = Math.min(50, allMatches.size()); // Increased from 30 to 50 for better coverage
        for (int i = 0; i < maxResults; i++) {
            MatchResult match = allMatches.get(i);

            // Include larger context around relevant line (10 lines before and after for important matches)
            int contextSize = match.relevanceScore >= 50 ? 10 : 5;
            int start = Math.max(0, match.lineNumber - contextSize);
            int end = Math.min(lines.length, match.lineNumber + contextSize + 1);

            result.append("=== РЕЛЕВАНТНА ІНФОРМАЦІЯ #").append(i + 1)
                  .append(" (рядок ").append(match.lineNumber + 1)
                  .append(", релевантність: ").append(match.relevanceScore).append(") ===\n");

            for (int j = start; j < end; j++) {
                if (j == match.lineNumber) {
                    result.append(">>> ").append(lines[j]).append(" <<<\n"); // Highlight the match
                } else {
                    result.append(lines[j]).append("\n");
                }
            }
            result.append("=== КІНЕЦЬ ІНФОРМАЦІЇ #").append(i + 1).append(" ===\n\n");
        }

        // If no matches found, include multiple samples from different parts of the file
        if (allMatches.isEmpty()) {
            result.append("=== ЗРАЗКИ З РІЗНИХ ЧАСТИН ФАЙЛУ ===\n");

            // Sample from beginning (after header)
            int sample1Start = headerSize;
            int sample1End = Math.min(lines.length, sample1Start + 1000);
            result.append("--- ПОЧАТОК ФАЙЛУ ---\n");
            for (int i = sample1Start; i < sample1End; i++) {
                result.append(lines[i]).append("\n");
            }

            // Sample from middle
            int sample2Start = Math.max(sample1End, lines.length / 2 - 500);
            int sample2End = Math.min(lines.length, sample2Start + 1000);
            result.append("--- СЕРЕДИНА ФАЙЛУ ---\n");
            for (int i = sample2Start; i < sample2End; i++) {
                result.append(lines[i]).append("\n");
            }

            // Sample from end
            int sample3Start = Math.max(sample2End, lines.length - 1000);
            int sample3End = lines.length;
            result.append("--- КІНЕЦЬ ФАЙЛУ ---\n");
            for (int i = sample3Start; i < sample3End; i++) {
                result.append(lines[i]).append("\n");
            }

            result.append("=== КІНЕЦЬ ЗРАЗКІВ ===\n");
            System.out.println("[Enhanced Search] No matches found, included samples from beginning, middle, and end of file");
        }

        String finalResult = result.toString();
        System.out.println("[Enhanced Search] Final result: " + finalResult.length() + " characters from " + maxResults + " top matches");

        return finalResult;
    }

    /**
     * Full file analysis - analyzes ENTIRE file but limits results for API (DEPRECATED - use enhancedContextSearch)
     */
    private static String fullFileAnalysis(String[] lines, String[] queryWords, String query) {
        StringBuilder result = new StringBuilder();

        System.out.println("[Full Analysis] Analyzing ENTIRE file with " + lines.length + " lines");

        // Always include header (first 100 lines for context)
        result.append("=== ЗАГОЛОВОК ФАЙЛУ ===\n");
        int headerSize = Math.min(100, lines.length);
        for (int i = 0; i < headerSize; i++) {
            result.append(lines[i]).append("\n");
        }
        result.append("=== КІНЕЦЬ ЗАГОЛОВКУ ===\n\n");

        // Analyze EVERY line in the file
        java.util.List<MatchResult> allMatches = new java.util.ArrayList<>();

        for (int i = 0; i < lines.length; i++) {
            String line = lines[i].toLowerCase();
            boolean isRelevant = false;

            // Check if line contains any query words
            for (String word : queryWords) {
                if (word.length() > 2 && line.contains(word)) {
                    isRelevant = true;
                    break;
                }
            }

            if (isRelevant) {
                // Calculate relevance score (more matches = higher score)
                int relevanceScore = 0;
                for (String word : queryWords) {
                    if (word.length() > 2 && line.contains(word)) {
                        relevanceScore++;
                    }
                }

                allMatches.add(new MatchResult(i, relevanceScore, lines[i]));
            }
        }

        System.out.println("[Full Analysis] Found " + allMatches.size() + " relevant lines in entire file");

        // Sort by relevance score (highest first)
        allMatches.sort((a, b) -> Integer.compare(b.relevanceScore, a.relevanceScore));

        // Include top 50 most relevant results with context
        int maxResults = Math.min(50, allMatches.size());
        for (int i = 0; i < maxResults; i++) {
            MatchResult match = allMatches.get(i);

            // Include context around relevant line (5 lines before and after)
            int start = Math.max(0, match.lineNumber - 5);
            int end = Math.min(lines.length, match.lineNumber + 6);

            result.append("=== РЕЛЕВАНТНА ІНФОРМАЦІЯ #").append(i + 1)
                  .append(" (рядок ").append(match.lineNumber + 1)
                  .append(", релевантність: ").append(match.relevanceScore).append(") ===\n");

            for (int j = start; j < end; j++) {
                if (j == match.lineNumber) {
                    result.append(">>> ").append(lines[j]).append(" <<<\n"); // Highlight the match
                } else {
                    result.append(lines[j]).append("\n");
                }
            }
            result.append("=== КІНЕЦЬ ІНФОРМАЦІЇ #").append(i + 1).append(" ===\n\n");
        }

        // If no matches found, include a sample from the middle of the file
        if (allMatches.isEmpty()) {
            result.append("=== ЗРАЗОК З СЕРЕДИНИ ФАЙЛУ ===\n");
            int sampleStart = Math.max(headerSize, lines.length / 2 - 1000);
            int sampleEnd = Math.min(lines.length, sampleStart + 2000);

            for (int i = sampleStart; i < sampleEnd; i++) {
                result.append(lines[i]).append("\n");
            }
            result.append("=== КІНЕЦЬ ЗРАЗКУ ===\n");
            System.out.println("[Full Analysis] No matches found, included sample from middle of file");
        }

        String finalResult = result.toString();
        System.out.println("[Full Analysis] Final result: " + finalResult.length() + " characters from " + maxResults + " top matches");

        return finalResult;
    }

    /**
     * Helper class for storing match results with relevance scoring
     */
    private static class MatchResult {
        int lineNumber;
        int relevanceScore;
        String lineContent;

        MatchResult(int lineNumber, int relevanceScore, String lineContent) {
            this.lineNumber = lineNumber;
            this.relevanceScore = relevanceScore;
            this.lineContent = lineContent;
        }
    }

    /**
     * Smart chunking for very large files (100k+ lines)
     */
    private static String smartChunkSearch(String[] lines, String[] queryWords, String query) {
        StringBuilder result = new StringBuilder();
        final int CHUNK_SIZE = 1000; // Process 1000 lines at a time
        final int MAX_CHUNKS = 10;   // Maximum 10 chunks to process
        int chunksProcessed = 0;

        System.out.println("[Smart Chunk] Processing " + lines.length + " lines in chunks of " + CHUNK_SIZE);

        // Always include header (first 100 lines)
        result.append("=== ЗАГОЛОВОК ФАЙЛУ ===\n");
        int headerSize = Math.min(100, lines.length);
        for (int i = 0; i < headerSize; i++) {
            result.append(lines[i]).append("\n");
        }
        result.append("=== КІНЕЦЬ ЗАГОЛОВКУ ===\n\n");

        // Search through chunks
        for (int chunkStart = headerSize; chunkStart < lines.length && chunksProcessed < MAX_CHUNKS; chunkStart += CHUNK_SIZE) {
            int chunkEnd = Math.min(chunkStart + CHUNK_SIZE, lines.length);

            // Check if this chunk contains relevant information
            boolean chunkHasRelevantInfo = false;
            StringBuilder chunkContent = new StringBuilder();

            for (int i = chunkStart; i < chunkEnd; i++) {
                String line = lines[i].toLowerCase();
                boolean lineIsRelevant = false;

                for (String word : queryWords) {
                    if (word.length() > 2 && line.contains(word)) {
                        lineIsRelevant = true;
                        chunkHasRelevantInfo = true;
                        break;
                    }
                }

                if (lineIsRelevant) {
                    // Include context around relevant line
                    int contextStart = Math.max(chunkStart, i - 3);
                    int contextEnd = Math.min(chunkEnd, i + 4);

                    for (int j = contextStart; j < contextEnd; j++) {
                        chunkContent.append(lines[j]).append("\n");
                    }
                    chunkContent.append("---\n");
                }
            }

            if (chunkHasRelevantInfo) {
                result.append("=== РЕЛЕВАНТНИЙ ФРАГМЕНТ (рядки ").append(chunkStart).append("-").append(chunkEnd).append(") ===\n");
                result.append(chunkContent.toString());
                result.append("=== КІНЕЦЬ ФРАГМЕНТУ ===\n\n");
                chunksProcessed++;

                System.out.println("[Smart Chunk] Found relevant info in chunk " + chunkStart + "-" + chunkEnd);
            }
        }

        String finalResult = result.toString();
        System.out.println("[Smart Chunk] Final result: " + finalResult.length() + " characters from " + chunksProcessed + " relevant chunks");

        // If no relevant chunks found, return header + sample
        if (chunksProcessed == 0) {
            result.append("=== ЗРАЗОК КОНТЕНТУ ===\n");
            int sampleSize = Math.min(2000, lines.length - headerSize);
            for (int i = headerSize; i < headerSize + sampleSize; i++) {
                result.append(lines[i]).append("\n");
            }
            result.append("=== КІНЕЦЬ ЗРАЗКУ ===\n");
            System.out.println("[Smart Chunk] No relevant chunks found, returning header + sample");
        }

        return result.toString();
    }

    /**
     * Enhanced search for medium files (10k-50k lines)
     */
    private static String enhancedSearch(String[] lines, String[] queryWords, String query) {
        StringBuilder result = new StringBuilder();
        final int MAX_RESULTS = 20; // Maximum 20 relevant sections
        int resultsFound = 0;

        System.out.println("[Enhanced Search] Processing " + lines.length + " lines");

        // Include header (first 50 lines)
        result.append("=== ЗАГОЛОВОК ===\n");
        int headerSize = Math.min(50, lines.length);
        for (int i = 0; i < headerSize; i++) {
            result.append(lines[i]).append("\n");
        }
        result.append("=== КІНЕЦЬ ЗАГОЛОВКУ ===\n\n");

        // Search for relevant lines with better context
        for (int i = headerSize; i < lines.length && resultsFound < MAX_RESULTS; i++) {
            String line = lines[i].toLowerCase();
            boolean isRelevant = false;

            for (String word : queryWords) {
                if (word.length() > 2 && line.contains(word)) {
                    isRelevant = true;
                    break;
                }
            }

            if (isRelevant) {
                // Include larger context (10 lines before and after)
                int start = Math.max(headerSize, i - 10);
                int end = Math.min(lines.length, i + 11);

                result.append("=== РЕЛЕВАНТНА ІНФОРМАЦІЯ #").append(resultsFound + 1).append(" ===\n");
                for (int j = start; j < end; j++) {
                    result.append(lines[j]).append("\n");
                }
                result.append("=== КІНЕЦЬ ІНФОРМАЦІЇ #").append(resultsFound + 1).append(" ===\n\n");
                resultsFound++;

                // Skip ahead to avoid overlapping results
                i = end;
            }
        }

        System.out.println("[Enhanced Search] Found " + resultsFound + " relevant sections");
        return result.toString();
    }

    /**
     * Original search for smaller files (<10k lines)
     */
    private static String originalSearch(String[] lines, String[] queryWords, String query) {
        StringBuilder relevantContext = new StringBuilder();

        // Always include first 50 lines (general info)
        int headerLines = Math.min(50, lines.length);
        for (int i = 0; i < headerLines; i++) {
            relevantContext.append(lines[i]).append("\n");
        }

        // Find lines containing query keywords
        for (int i = 0; i < lines.length; i++) {
            String line = lines[i].toLowerCase();
            boolean isRelevant = false;

            // Check if line contains any query words
            for (String word : queryWords) {
                if (word.length() > 2 && line.contains(word)) {
                    isRelevant = true;
                    break;
                }
            }

            if (isRelevant) {
                // Include context around relevant line (5 lines before and after)
                int start = Math.max(0, i - 5);
                int end = Math.min(lines.length, i + 6);

                relevantContext.append("\n=== РЕЛЕВАНТНА ІНФОРМАЦІЯ ===\n");
                for (int j = start; j < end; j++) {
                    relevantContext.append(lines[j]).append("\n");
                }
                relevantContext.append("=== КІНЕЦЬ РЕЛЕВАНТНОЇ ІНФОРМАЦІЇ ===\n\n");
            }
        }

        String result = relevantContext.toString();

        // If filtered context is too small, return more of original
        if (result.length() < 1000) {
            StringBuilder moreContext = new StringBuilder(result);
            int additionalLines = Math.min(2000, lines.length - headerLines);
            for (int i = headerLines; i < headerLines + additionalLines; i++) {
                moreContext.append(lines[i]).append("\n");
            }
            return moreContext.toString();
        }

        return result;
    }

    /**
     * Extract player name from query for distance calculations
     */
    private static String extractPlayerNameFromQuery(String query) {
        String lowerQuery = query.toLowerCase();

        // Common patterns for player-related queries
        String[] patterns = {
            "гравця\\s+(\\w+)",
            "гравець\\s+(\\w+)",
            "до\\s+(\\w+)",
            "біля\\s+(\\w+)",
            "поруч\\s+з\\s+(\\w+)",
            "від\\s+(\\w+)",
            "player\\s+(\\w+)"
        };

        for (String pattern : patterns) {
            java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
            java.util.regex.Matcher m = p.matcher(lowerQuery);
            if (m.find()) {
                return m.group(1);
            }
        }

        return null;
    }

    /**
     * Smart truncation that preserves complete sentences
     */
    private static String smartTruncate(String text, int maxLength) {
        if (text.length() <= maxLength) {
            return text;
        }

        // Try to find the last complete sentence within the limit
        String truncated = text.substring(0, maxLength);

        // Look for sentence endings (. ! ?) working backwards
        int lastSentenceEnd = -1;
        for (int i = truncated.length() - 1; i >= 0; i--) {
            char c = truncated.charAt(i);
            if (c == '.' || c == '!' || c == '?') {
                lastSentenceEnd = i;
                break;
            }
        }

        // If we found a sentence ending, use it
        if (lastSentenceEnd > maxLength * 0.7) { // Only if it's not too short (at least 70% of limit)
            return truncated.substring(0, lastSentenceEnd + 1);
        }

        // Otherwise, find the last complete word
        int lastSpace = truncated.lastIndexOf(' ');
        if (lastSpace > maxLength * 0.8) { // Only if it's not too short (at least 80% of limit)
            return truncated.substring(0, lastSpace) + "...";
        }

        // As last resort, hard truncate with ellipsis
        return truncated.substring(0, maxLength - 3) + "...";
    }
}
